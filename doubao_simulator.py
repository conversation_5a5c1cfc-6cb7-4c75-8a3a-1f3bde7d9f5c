#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包API请求模拟器
根据豆包.md文件中的配置参数模拟API请求
"""

import requests
import json

from urllib.parse import urlencode

class DoubaoAPI:
    def __init__(self):
        # 基础URL和参数（从文件中提取）
        self.base_url = "https://www.doubao.com/samantha/chat/completion"
        
        # URL参数（去除msToken和a_bogus）
        self.url_params = {
            "aid": "497858",
            "device_id": "7532812407958914560",
            "device_platform": "web",
            "language": "zh",
            "pc_version": "2.29.2",
            "pkg_type": "release_version",
            "real_aid": "497858",
            "region": "CN",
            "samantha_web": "1",
            "sys_region": "CN",
            "tea_uuid": "7532812413725918772",
            "use-olympus-account": "1",
            "version_code": "20800",
            "web_id": "7532812413725918772"
        }
        
        # 请求头
        self.headers = {
            "Host": "www.doubao.com",
            "Connection": "keep-alive",
            "Content-Length": "528",
            "x-flow-trace": "04-000bf2b2b55cce5900091fb0d0829c7e-000aac4cf71a6969-01",
            "sec-ch-ua-platform": '"Android"',
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            "sec-ch-ua-mobile": "?1",
            "Agw-Js-Conv": "str, str",
            "last-event-id": "undefined",
            "User-Agent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 EdgA/*********",
            "content-type": "application/json",
            "Accept": "*/*",
            "Origin": "https://www.doubao.com",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://www.doubao.com/chat/local_6018814922950261?type=2",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            # Cookie简化版本（去除敏感信息）
            "Cookie": "i18next=zh; flow_user_country=CN; is_staff_user=false"
        }
        
        # 请求体数据
        self.request_data = {
            "messages": [
                {
                    "content": '{"text":"找点美女视频"}',
                    "content_type": 2001,
                    "attachments": [],
                    "references": []
                }
            ],
            "completion_option": {
                "is_regen": False,
                "with_suggest": True,
                "need_create_conversation": True,
                "launch_stage": 1,
                "is_replace": False,
                "is_delete": False,
                "message_from": 0,
                "use_deep_think": False,
                "use_auto_cot": True,
                "resend_for_regen": False,
                "event_id": "0"
            },
            "evaluate_option": {
                "web_ab_params": ""
            },
            "conversation_id": "0",
            "local_conversation_id": "local_6018814922950261",
            "local_message_id": "e56b2bc0-6d2c-11f0-8b9e-dbf1c3c4a16c"
        }

    def build_url(self):
        """构建完整的请求URL"""
        return f"{self.base_url}?{urlencode(self.url_params)}"

    def send_request(self, message_text="找点美女视频"):
        """发送请求到豆包API"""
        # 更新消息内容
        self.request_data["messages"][0]["content"] = json.dumps({"text": message_text})
        
        # 构建URL
        url = self.build_url()
        
        # 更新Content-Length
        json_data = json.dumps(self.request_data, ensure_ascii=False)
        self.headers["Content-Length"] = str(len(json_data.encode('utf-8')))
        
        print(f"发送请求到: {url}")
        print(f"请求数据: {json.dumps(self.request_data, ensure_ascii=False, indent=2)}")
        print("-" * 50)
        
        try:
            # 发送POST请求
            response = requests.post(
                url,
                headers=self.headers,
                json=self.request_data,
                stream=True,  # 因为是Server-Sent Events
                timeout=30
            )
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print("-" * 50)
            
            if response.status_code == 200:
                return self.parse_sse_response(response)
            else:
                print(f"请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
            return None

    def parse_sse_response(self, response):
        """解析Server-Sent Events响应"""
        print("开始解析SSE响应:")
        events = []
        
        try:
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    print(f"收到数据: {line}")
                    
                    if line.startswith("data: "):
                        data_content = line[6:]  # 去掉"data: "前缀
                        try:
                            event_data = json.loads(data_content)
                            events.append(event_data)
                            
                            # 解析事件内容
                            if "event_data" in event_data:
                                inner_data = json.loads(event_data["event_data"])
                                print(f"事件类型: {event_data.get('event_type')}")
                                print(f"事件ID: {event_data.get('event_id')}")
                                print(f"事件内容: {json.dumps(inner_data, ensure_ascii=False, indent=2)}")
                                print("-" * 30)
                                
                        except json.JSONDecodeError as e:
                            print(f"JSON解析错误: {e}")
                            print(f"原始数据: {data_content}")
                            
        except Exception as e:
            print(f"解析响应时出错: {e}")
            
        return events




def main():
    """主函数"""
    api = DoubaoAPI()

    print("豆包API请求工具启动")
    print("=" * 50)

    # 发送真实请求
    print("正在发送请求到豆包API...")
    result = api.send_request("找点美女视频")

    if result:
        print(f"\n请求成功，收到 {len(result)} 个响应事件")
    else:
        print("\n请求失败，请检查网络连接或参数配置")


if __name__ == "__main__":
    main()
