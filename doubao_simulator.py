#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包API请求模拟器
根据豆包.md文件中的配置参数模拟API请求
"""

import requests
import json
import time
from urllib.parse import urlencode

class DoubaoSimulator:
    def __init__(self):
        # 基础URL和参数（从文件中提取）
        self.base_url = "https://www.doubao.com/samantha/chat/completion"
        
        # URL参数（去除msToken和a_bogus）
        self.url_params = {
            "aid": "497858",
            "device_id": "7532812407958914560",
            "device_platform": "web",
            "language": "zh",
            "pc_version": "2.29.2",
            "pkg_type": "release_version",
            "real_aid": "497858",
            "region": "CN",
            "samantha_web": "1",
            "sys_region": "CN",
            "tea_uuid": "7532812413725918772",
            "use-olympus-account": "1",
            "version_code": "20800",
            "web_id": "7532812413725918772"
        }
        
        # 请求头
        self.headers = {
            "Host": "www.doubao.com",
            "Connection": "keep-alive",
            "Content-Length": "528",
            "x-flow-trace": "04-000bf2b2b55cce5900091fb0d0829c7e-000aac4cf71a6969-01",
            "sec-ch-ua-platform": '"Android"',
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            "sec-ch-ua-mobile": "?1",
            "Agw-Js-Conv": "str, str",
            "last-event-id": "undefined",
            "User-Agent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 EdgA/*********",
            "content-type": "application/json",
            "Accept": "*/*",
            "Origin": "https://www.doubao.com",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://www.doubao.com/chat/local_6018814922950261?type=2",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            # Cookie简化版本（去除敏感信息）
            "Cookie": "i18next=zh; flow_user_country=CN; is_staff_user=false"
        }
        
        # 请求体数据
        self.request_data = {
            "messages": [
                {
                    "content": '{"text":"找点美女视频"}',
                    "content_type": 2001,
                    "attachments": [],
                    "references": []
                }
            ],
            "completion_option": {
                "is_regen": False,
                "with_suggest": True,
                "need_create_conversation": True,
                "launch_stage": 1,
                "is_replace": False,
                "is_delete": False,
                "message_from": 0,
                "use_deep_think": False,
                "use_auto_cot": True,
                "resend_for_regen": False,
                "event_id": "0"
            },
            "evaluate_option": {
                "web_ab_params": ""
            },
            "conversation_id": "0",
            "local_conversation_id": "local_6018814922950261",
            "local_message_id": "e56b2bc0-6d2c-11f0-8b9e-dbf1c3c4a16c"
        }

    def build_url(self):
        """构建完整的请求URL"""
        return f"{self.base_url}?{urlencode(self.url_params)}"

    def send_request(self, message_text="找点美女视频"):
        """发送请求到豆包API"""
        # 更新消息内容
        self.request_data["messages"][0]["content"] = json.dumps({"text": message_text})
        
        # 构建URL
        url = self.build_url()
        
        # 更新Content-Length
        json_data = json.dumps(self.request_data, ensure_ascii=False)
        self.headers["Content-Length"] = str(len(json_data.encode('utf-8')))
        
        print(f"发送请求到: {url}")
        print(f"请求数据: {json.dumps(self.request_data, ensure_ascii=False, indent=2)}")
        print("-" * 50)
        
        try:
            # 发送POST请求
            response = requests.post(
                url,
                headers=self.headers,
                json=self.request_data,
                stream=True,  # 因为是Server-Sent Events
                timeout=30
            )
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print("-" * 50)
            
            if response.status_code == 200:
                return self.parse_sse_response(response)
            else:
                print(f"请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
            return None

    def parse_sse_response(self, response):
        """解析Server-Sent Events响应"""
        print("开始解析SSE响应:")
        events = []
        
        try:
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    print(f"收到数据: {line}")
                    
                    if line.startswith("data: "):
                        data_content = line[6:]  # 去掉"data: "前缀
                        try:
                            event_data = json.loads(data_content)
                            events.append(event_data)
                            
                            # 解析事件内容
                            if "event_data" in event_data:
                                inner_data = json.loads(event_data["event_data"])
                                print(f"事件类型: {event_data.get('event_type')}")
                                print(f"事件ID: {event_data.get('event_id')}")
                                print(f"事件内容: {json.dumps(inner_data, ensure_ascii=False, indent=2)}")
                                print("-" * 30)
                                
                        except json.JSONDecodeError as e:
                            print(f"JSON解析错误: {e}")
                            print(f"原始数据: {data_content}")
                            
        except Exception as e:
            print(f"解析响应时出错: {e}")
            
        return events

    def simulate_response(self, message_text="找点美女视频"):
        """模拟豆包API响应（基于文件中的示例）"""
        print("=== 模拟豆包API响应 ===")
        
        # 模拟的响应事件
        mock_events = [
            {
                "event_type": 2002,
                "event_id": "0",
                "event_data": {
                    "message_id": "14250436386201858",
                    "local_message_id": "e56b2bc0-6d2c-11f0-8b9e-dbf1c3c4a16c",
                    "conversation_id": "14264902952679426",
                    "local_conversation_id": "local_6018814922950261",
                    "section_id": "14264902952679682",
                    "message_index": 1,
                    "conversation_type": 5
                }
            },
            {
                "event_type": 2010,
                "event_id": "1",
                "event_data": {
                    "type": "seed_intention",
                    "seed_intention": {
                        "intention": "browsing",
                        "detail": "rich_media_only_video",
                        "seed_agent_name": "Agent-RichMedia"
                    }
                }
            },
            {
                "event_type": 2001,
                "event_id": "2",
                "event_data": {
                    "message": {
                        "content_type": 2003,
                        "content": '{"type":1,"text":"正在搜索"}',
                        "id": "4fe7ee51-9ef6-455f-8aca-e0876ddb9262"
                    },
                    "status": 4,
                    "is_delta": True
                }
            },
            {
                "event_type": 2001,
                "event_id": "3",
                "event_data": {
                    "message": {
                        "content_type": 2008,
                        "content": '{"text":"这些是我帮你搜索到的视频。"}',
                        "id": "4fe7ee51-9ef6-455f-8aca-e0876ddb9262"
                    },
                    "status": 1,
                    "is_finish": True,
                    "has_suggest": True
                }
            }
        ]
        
        for i, event in enumerate(mock_events):
            print(f"事件 {i+1}:")
            print(f"类型: {event['event_type']}")
            print(f"ID: {event['event_id']}")
            print(f"数据: {json.dumps(event['event_data'], ensure_ascii=False, indent=2)}")
            print("-" * 30)
            time.sleep(0.5)  # 模拟实时响应
            
        return mock_events


def main():
    """主函数"""
    simulator = DoubaoSimulator()
    
    print("豆包API模拟器启动")
    print("=" * 50)
    
    # 选择模式
    mode = input("选择模式 (1: 发送真实请求, 2: 模拟响应): ").strip()
    
    if mode == "1":
        # 发送真实请求
        message = input("请输入要发送的消息 (默认: 找点美女视频): ").strip()
        if not message:
            message = "找点美女视频"
            
        result = simulator.send_request(message)
        if result:
            print(f"收到 {len(result)} 个事件")
        else:
            print("请求失败")
            
    elif mode == "2":
        # 模拟响应
        message = input("请输入要模拟的消息 (默认: 找点美女视频): ").strip()
        if not message:
            message = "找点美女视频"
            
        result = simulator.simulate_response(message)
        print(f"模拟了 {len(result)} 个事件")
        
    else:
        print("无效的模式选择")


if __name__ == "__main__":
    main()
