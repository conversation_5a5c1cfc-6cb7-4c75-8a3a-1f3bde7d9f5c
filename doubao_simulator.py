#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包API请求模拟器
根据豆包.md文件中的配置参数模拟API请求
"""

import requests
import json

from urllib.parse import urlencode

class DoubaoAPI:
    def __init__(self):
        # 基础URL和参数（从文件中提取）
        self.base_url = "https://www.doubao.com/samantha/chat/completion"
        
        # URL参数（去除msToken和a_bogus）
        self.url_params = {
            "aid": "497858",
            "device_id": "7532812407958914560",
            "device_platform": "web",
            "language": "zh",
            "pc_version": "2.29.2",
            "pkg_type": "release_version",
            "real_aid": "497858",
            "region": "CN",
            "samantha_web": "1",
            "sys_region": "CN",
            "tea_uuid": "7532812413725918772",
            "use-olympus-account": "1",
            "version_code": "20800",
            "web_id": "7532812413725918772"
        }
        
        # 请求头
        self.headers = {
            "Host": "www.doubao.com",
            "Connection": "keep-alive",
            "Content-Length": "528",
            "x-flow-trace": "04-000bf2b2b55cce5900091fb0d0829c7e-000aac4cf71a6969-01",
            "sec-ch-ua-platform": '"Android"',
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            "sec-ch-ua-mobile": "?1",
            "Agw-Js-Conv": "str, str",
            "last-event-id": "undefined",
            "User-Agent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 EdgA/*********",
            "content-type": "application/json",
            "Accept": "*/*",
            "Origin": "https://www.doubao.com",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://www.doubao.com/chat/local_6018814922950261?type=2",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            # Cookie完整版本（从抓包文件中提取）
            "Cookie": "hook_slardar_session_id=2025073017592839920BEBA2D613EF2863,ttwid=1%7CiMFHx1Da_BetezZn05zfft9t1j-kjns3qTR_5L6OV08%7C1753869568%7Ca4235a632c824c9806738adf2a81d8f6c10d14bc68364c986d1de738467b554c; i18next=zh; _ga=GA1.1.*********.1753869579; flow_user_country=CN; ttcid=680f850c9d6c4cef8c2896d7eb66eabc38; s_v_web_id=verify_mdpso4wy_jIHunFlc_ubrJ_4rnM_AFJ2_lnMQE6tXGixX; passport_csrf_token=d24a324fc83e183d82d2ff376865d10a; passport_csrf_token_default=d24a324fc83e183d82d2ff376865d10a; passport_mfa_token=CjhmMmxq74s45voqX5p6YdkTf1aOmqKOzpFl16J07%2BVgDkKOQQEZLdf9yskI1i%2BDekA5EUy5DoOLVBpKCjwAAAAAAAAAAAAAT0vzn5wH%2F%2BIeVMweyqaIHWGbns9B9a58PfbsvTRkyotTHxZwlgjMUlSxPv1wGWBuNS0Q1474DRj2sdFsIAIiAQOsujFr; d_ticket=560ba6c243be3cc7d1a5ef686b58aeaac2adc; odin_tt=92daead88f19c6eeeddec32e138fe8b384de6e89757f7daecc7e9047933659c6fbd9ccfa1aaf65e89782b55c3b05ebc374fbbb39e4a7380f03ee65be5f2b5b7b; n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI; passport_auth_status=30b6a10d87ae77cce375cafda8fc3f2a%2C; passport_auth_status_ss=30b6a10d87ae77cce375cafda8fc3f2a%2C; sid_guard=76f286fcb74fe1c863d95e984c75a39e%7C1753869599%7C5184000%7CSun%2C+28-Sep-2025+09%3A59%3A59+GMT; uid_tt=f0649b32573486f603a5910e87efee82; uid_tt_ss=f0649b32573486f603a5910e87efee82; sid_tt=76f286fcb74fe1c863d95e984c75a39e; sessionid=76f286fcb74fe1c863d95e984c75a39e; sessionid_ss=76f286fcb74fe1c863d95e984c75a39e; session_tlb_tag=sttt%7C8%7CdvKG_LdP4chj2V6YTHWjnv_________P3rzelBNJ06HunzJflrwKP1i5fPvlQLpG3XaOLaXNUxU%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KGRhOTNiZTBlZjEzZTMxZTY0MmEwZTNmZmQyYjlkZjY2MmRhMzBkODQKIAjZtNCbo82jBhCf2qfEBhjCsR4gDDC57oy9BjgCQPEHGgJsZiIgNzZmMjg2ZmNiNzRmZTFjODYzZDk1ZTk4NGM3NWEzOWU; ssid_ucp_v1=1.0.0-KGRhOTNiZTBlZjEzZTMxZTY0MmEwZTNmZmQyYjlkZjY2MmRhMzBkODQKIAjZtNCbo82jBhCf2qfEBhjCsR4gDDC57oy9BjgCQPEHGgJsZiIgNzZmMjg2ZmNiNzRmZTFjODYzZDk1ZTk4NGM3NWEzOWU; flow_ssr_sidebar_expand=1; gd_random=eyJwZXJjZW50IjowLjk1MzU1MTg3ODY2MzY3NDIsIm1hdGNoIjp0cnVlfQ==.Aj6mewr53/erNAFOt/dHmFCFMSr1U6VZ70mtAOfq9qw=; ttwid=1%7CG_ugBSA7aPKN7hVyth9pnegG5dZcVXYm0c59Fglz-OY%7C1753869826%7C973d52659f005b222716929a668d540b9afefa363508ccff76ae4b5115ca307f; passport_fe_beating_status=true; _ga_G8EP5CG8VZ=GS2.1.s1753869579$o1$g1$t1753869832$j56$l0$h0; tt_scid=W-vtjfjm4UdGEU0roTki0VqJLUwNs5migJ6Duftzfj2n2vZgeXbH-dDduOQM9i.-f096"
        }
        
        # 请求体数据
        self.request_data = {
            "messages": [
                {
                    "content": '{"text":"找点美女视频"}',
                    "content_type": 2001,
                    "attachments": [],
                    "references": []
                }
            ],
            "completion_option": {
                "is_regen": False,
                "with_suggest": True,
                "need_create_conversation": True,
                "launch_stage": 1,
                "is_replace": False,
                "is_delete": False,
                "message_from": 0,
                "use_deep_think": False,
                "use_auto_cot": True,
                "resend_for_regen": False,
                "event_id": "0"
            },
            "evaluate_option": {
                "web_ab_params": ""
            },
            "conversation_id": "0",
            "local_conversation_id": "local_6018814922950261",
            "local_message_id": "e56b2bc0-6d2c-11f0-8b9e-dbf1c3c4a16c"
        }

    def build_url(self):
        """构建完整的请求URL"""
        return f"{self.base_url}?{urlencode(self.url_params)}"

    def send_request(self, message_text="找点美女视频"):
        """发送请求到豆包API"""
        # 更新消息内容
        self.request_data["messages"][0]["content"] = json.dumps({"text": message_text})
        
        # 构建URL
        url = self.build_url()
        
        # 更新Content-Length
        json_data = json.dumps(self.request_data, ensure_ascii=False)
        self.headers["Content-Length"] = str(len(json_data.encode('utf-8')))
        
        print(f"发送请求到: {url}")
        print(f"请求数据: {json.dumps(self.request_data, ensure_ascii=False, indent=2)}")
        print("-" * 50)
        
        try:
            # 发送POST请求
            response = requests.post(
                url,
                headers=self.headers,
                json=self.request_data,
                stream=True,  # 因为是Server-Sent Events
                timeout=(10, 30)  # 连接超时10秒，读取超时30秒
            )
            
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            print("-" * 50)
            
            if response.status_code == 200:
                print("开始读取响应内容...")
                try:
                    # 设置较短的读取超时
                    import signal

                    def timeout_handler(signum, frame):
                        raise TimeoutError("读取响应超时")

                    signal.signal(signal.SIGALRM, timeout_handler)
                    signal.alarm(10)  # 10秒超时

                    try:
                        # 直接读取前几KB的内容
                        content = ""
                        for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                            content += chunk
                            if len(content) > 5000:  # 限制读取长度
                                break

                        signal.alarm(0)  # 取消超时
                        print(f"收到响应内容 ({len(content)} 字符):")
                        print(content)
                        print("-" * 50)
                        return [{"raw_content": content}]

                    except TimeoutError:
                        signal.alarm(0)
                        print("读取响应超时，可能服务器没有返回数据")
                        return [{"error": "timeout"}]

                except Exception as e:
                    print(f"读取响应内容时出错: {e}")
                    return None
            else:
                print(f"请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
            return None

    def parse_sse_response(self, response):
        """解析Server-Sent Events响应"""
        print("开始解析SSE响应:")
        events = []
        line_count = 0

        try:
            for line in response.iter_lines(decode_unicode=True, chunk_size=1024):
                line_count += 1
                if line_count > 100:  # 防止无限循环
                    print("接收数据过多，停止解析")
                    break

                if line:
                    print(f"收到数据: {line}")

                    if line.startswith("data: "):
                        data_content = line[6:]  # 去掉"data: "前缀
                        try:
                            event_data = json.loads(data_content)
                            events.append(event_data)

                            # 检查是否是错误响应
                            if "code" in event_data and "message" in event_data:
                                print(f"\n❌ API返回错误:")
                                print(f"错误代码: {event_data.get('code')}")
                                print(f"错误信息: {event_data.get('message')}")
                                print("这说明请求被拒绝了，可能是认证信息无效")
                                print("-" * 50)
                                return events

                            # 解析正常事件内容
                            if "event_data" in event_data:
                                inner_data = json.loads(event_data["event_data"])
                                print(f"✅ 事件类型: {event_data.get('event_type')}")
                                print(f"事件ID: {event_data.get('event_id')}")
                                print(f"事件内容: {json.dumps(inner_data, ensure_ascii=False, indent=2)}")
                                print("-" * 30)

                        except json.JSONDecodeError as e:
                            print(f"JSON解析错误: {e}")
                            print(f"原始数据: {data_content}")
                else:
                    # 空行，可能表示事件结束
                    if line_count > 5:  # 收到几行后如果是空行就退出
                        print("收到空行，可能是数据流结束")
                        break

        except Exception as e:
            print(f"解析响应时出错: {e}")

        print(f"解析完成，共收到 {line_count} 行数据")
        return events




def show_expected_response():
    """显示基于抓包文件的预期响应格式"""
    print("=" * 60)
    print("基于您抓包文件的正确响应格式应该是：")
    print("=" * 60)

    expected_events = [
        {
            "event_type": 2002,
            "event_id": "0",
            "description": "会话初始化",
            "sample_data": {
                "message_id": "14250436386201858",
                "conversation_id": "14264902952679426",
                "conversation_type": 5
            }
        },
        {
            "event_type": 2010,
            "event_id": "1",
            "description": "意图识别 - 视频浏览",
            "sample_data": {
                "type": "seed_intention",
                "seed_intention": {
                    "intention": "browsing",
                    "detail": "rich_media_only_video",
                    "seed_agent_name": "Agent-RichMedia"
                }
            }
        },
        {
            "event_type": 2001,
            "event_id": "2",
            "description": "搜索状态",
            "sample_data": {
                "message": {
                    "content_type": 2003,
                    "content": '{"type":1,"text":"正在搜索"}'
                },
                "status": 4,
                "is_delta": True
            }
        },
        {
            "event_type": 2001,
            "event_id": "3",
            "description": "搜索完成",
            "sample_data": {
                "message": {
                    "content_type": 2008,
                    "content": '{"text":"这些是我帮你搜索到的视频。"}'
                },
                "status": 1,
                "is_finish": True
            }
        },
        {
            "event_type": 2001,
            "event_id": "4",
            "description": "视频搜索结果",
            "sample_data": {
                "message": {
                    "content_type": 2007,
                    "content": "包含抖音视频列表的JSON数据"
                }
            }
        }
    ]

    for i, event in enumerate(expected_events, 1):
        print(f"\n事件 {i}:")
        print(f"  类型: {event['event_type']}")
        print(f"  ID: {event['event_id']}")
        print(f"  描述: {event['description']}")
        print(f"  示例数据: {json.dumps(event['sample_data'], ensure_ascii=False, indent=4)}")
        print("-" * 50)

def main():
    """主函数"""
    api = DoubaoAPI()

    print("豆包API请求工具启动")
    print("=" * 50)

    # 发送真实请求
    print("正在发送请求到豆包API...")
    result = api.send_request("找点美女视频")

    if result:
        # 检查是否有错误
        has_error = any("code" in event and "message" in event for event in result)
        if has_error:
            print(f"\n❌ 请求被拒绝，收到错误响应（rate limited）")
            print("这说明Cookie可能已过期或请求频率过高")
            show_expected_response()
        else:
            print(f"\n✅ 请求成功，收到 {len(result)} 个响应事件")
    else:
        print("\n❌ 请求失败，请检查网络连接或参数配置")


if __name__ == "__main__":
    main()
